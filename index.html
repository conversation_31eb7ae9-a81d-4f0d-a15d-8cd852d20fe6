<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RealWear Smart Glass UI Designer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="design-environment">
        <!-- Design Toolbar -->
        <div class="toolbar">
            <h1>RealWear UI Designer</h1>
            <div class="device-info">
                <span>Resolution: 854 x 480</span>
                <span>FOV: 20°</span>
            </div>
            <div class="controls">
                <button id="toggleGrid">Show Grid</button>
                <button id="toggleRuler">Show Ruler</button>
                <button id="resetZoom">Reset Zoom</button>
                <select id="themeSelector">
                    <option value="light">Light Theme</option>
                    <option value="dark">Dark Theme</option>
                    <option value="industrial">Industrial Theme</option>
                </select>
            </div>
        </div>

        <!-- 主设计区域 -->
        <div class="design-area">
            <!-- RealWear设备模拟器 -->
            <div class="device-simulator">
                <div class="device-frame">
                    <div class="screen" id="realwearScreen">
                        <!-- Task Management System UI based on sample -->
                        <div class="ui-container">
                            <!-- Header with task info and time -->
                            <div class="task-header">
                                <div class="user-info">
                                    <span class="welcome-text">Testing (demo1)</span>
                                    <span class="current-time">09:16 AM</span>
                                </div>
                                <div class="step-info">
                                    <span class="step-text">Step 2 of 3</span>
                                </div>
                            </div>

                            <!-- Capture Video Interface -->
                            <div class="capture-interface">
                                <!-- Main title -->
                                <div class="capture-title">Capture video 2</div>

                                <!-- Main content area with border -->
                                <div class="capture-main-area">
                                    <!-- Content area -->
                                    <div class="capture-content">
                                        <!-- Left side - Image display -->
                                        <div class="image-display">
                                            <div class="image-container">
                                                <img src="sample.png" alt="Sample" class="sample-image">
                                                <div class="image-overlay">
                                                    <div class="overlay-text-top">Two</div>
                                                    <div class="overlay-text-bottom">2</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Vertical divider -->
                                        <div class="content-divider"></div>

                                        <!-- Right side - Capture info -->
                                        <div class="capture-info">
                                            <div class="capture-label">capture 2</div>
                                        </div>
                                    </div>

                                    <!-- Horizontal divider -->
                                    <div class="horizontal-divider"></div>

                                    <!-- Bottom controls -->
                                    <div class="capture-controls">
                                        <button class="control-btn record-btn" title="Record Video">
                                            <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <circle cx="12" cy="12" r="8"/>
                                            </svg>
                                        </button>
                                        <button class="control-btn camera-btn" title="Take Photo">
                                            <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M12 15.5c1.93 0 3.5-1.57 3.5-3.5S13.93 8.5 12 8.5 8.5 10.07 8.5 12s1.57 3.5 3.5 3.5z"/>
                                                <path d="M17.5 6.5h-3L13 4.5h-2L9.5 6.5h-3C5.67 6.5 5 7.17 5 8v8c0 .83.67 1.5 1.5 1.5h11c.83 0 1.5-.67 1.5-1.5V8c0-.83-.67-1.5-1.5-1.5z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Video Complete Modal -->
                                <div class="modal-overlay">
                                    <div class="modal-container">
                                        <div class="modal-header">
                                            <h3 class="modal-title">Video record complete.</h3>
                                        </div>
                                        <div class="modal-body">
                                            <p class="modal-question">Do you want to watch the recorded video?</p>
                                            <p class="modal-hint">(Choose your next action)</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button class="modal-btn watch-btn">WATCH</button>
                                            <button class="modal-btn proceed-btn">NO, PROCEED</button>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>

            <!-- Component Panel -->
            <div class="component-panel">
                <h3>UI Components</h3>
                <div class="component-list">
                    <div class="component-item" data-component="button">Button</div>
                    <div class="component-item" data-component="menu">Menu</div>
                    <div class="component-item" data-component="card">Card</div>
                    <div class="component-item" data-component="list">List</div>
                    <div class="component-item" data-component="form">Form</div>
                    <div class="component-item" data-component="qr">QR Code</div>
                </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <h3>Properties</h3>
            <div class="property-group">
                <label>Background Color:</label>
                <input type="color" id="bgColor" value="#000000">
            </div>
            <div class="property-group">
                <label>Text Color:</label>
                <input type="color" id="textColor" value="#ffffff">
            </div>
            <div class="property-group">
                <label>Font Size:</label>
                <input type="range" id="fontSize" min="12" max="24" value="16">
                <span id="fontSizeValue">16px</span>
            </div>
            <div class="property-group">
                <label>Padding:</label>
                <input type="range" id="padding" min="0" max="20" value="10">
                <span id="paddingValue">10px</span>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
