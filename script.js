// RealWear Smart Glass UI Designer JavaScript

class RealWearUIDesigner {
    constructor() {
        this.screen = document.getElementById('realwearScreen');
        this.currentTheme = 'dark';
        this.gridVisible = false;
        this.rulerVisible = false;
        
        this.initializeEventListeners();
        this.initializePropertyControls();
    }

    initializeEventListeners() {
        // 主题切换
        document.getElementById('themeSelector').addEventListener('change', (e) => {
            this.changeTheme(e.target.value);
        });

        // 网格切换
        document.getElementById('toggleGrid').addEventListener('click', () => {
            this.toggleGrid();
        });

        // 标尺切换
        document.getElementById('toggleRuler').addEventListener('click', () => {
            this.toggleRuler();
        });

        // 重置缩放
        document.getElementById('resetZoom').addEventListener('click', () => {
            this.resetZoom();
        });

        // 组件拖拽
        this.initializeComponentDrag();

        // 菜单项点击效果
        this.initializeMenuInteractions();
    }

    initializePropertyControls() {
        // 背景颜色控制
        document.getElementById('bgColor').addEventListener('change', (e) => {
            this.screen.style.backgroundColor = e.target.value;
        });

        // 文字颜色控制
        document.getElementById('textColor').addEventListener('change', (e) => {
            const uiContainer = this.screen.querySelector('.ui-container');
            uiContainer.style.color = e.target.value;
        });

        // 字体大小控制
        const fontSizeSlider = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        
        fontSizeSlider.addEventListener('input', (e) => {
            const size = e.target.value + 'px';
            fontSizeValue.textContent = size;
            this.screen.style.fontSize = size;
        });

        // 边距控制
        const paddingSlider = document.getElementById('padding');
        const paddingValue = document.getElementById('paddingValue');
        
        paddingSlider.addEventListener('input', (e) => {
            const padding = e.target.value + 'px';
            paddingValue.textContent = padding;
            const mainContent = this.screen.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.padding = padding;
            }
        });
    }

    changeTheme(theme) {
        // 移除旧主题类
        this.screen.classList.remove(`theme-${this.currentTheme}`);
        
        // 添加新主题类
        this.screen.classList.add(`theme-${theme}`);
        this.currentTheme = theme;

        // 根据主题调整样式
        switch(theme) {
            case 'light':
                this.screen.style.backgroundColor = '#ffffff';
                this.screen.style.color = '#333333';
                break;
            case 'dark':
                this.screen.style.backgroundColor = '#000000';
                this.screen.style.color = '#ffffff';
                break;
            case 'industrial':
                this.screen.style.backgroundColor = '#2c3e50';
                this.screen.style.color = '#ecf0f1';
                break;
        }
    }

    toggleGrid() {
        this.gridVisible = !this.gridVisible;
        
        if (this.gridVisible) {
            if (!this.screen.querySelector('.grid-overlay')) {
                const gridOverlay = document.createElement('div');
                gridOverlay.className = 'grid-overlay';
                this.screen.appendChild(gridOverlay);
            }
            document.getElementById('toggleGrid').textContent = 'Hide Grid';
        } else {
            const gridOverlay = this.screen.querySelector('.grid-overlay');
            if (gridOverlay) {
                gridOverlay.remove();
            }
            document.getElementById('toggleGrid').textContent = 'Show Grid';
        }
    }

    toggleRuler() {
        this.rulerVisible = !this.rulerVisible;
        
        if (this.rulerVisible) {
            this.createRulers();
            document.getElementById('toggleRuler').textContent = 'Hide Ruler';
        } else {
            this.removeRulers();
            document.getElementById('toggleRuler').textContent = 'Show Ruler';
        }
    }

    createRulers() {
        // 创建水平标尺
        const horizontalRuler = document.createElement('div');
        horizontalRuler.className = 'ruler horizontal-ruler';
        horizontalRuler.style.cssText = `
            position: absolute;
            top: -20px;
            left: 0;
            width: 100%;
            height: 20px;
            background: rgba(255,255,255,0.9);
            border-bottom: 1px solid #ccc;
            font-size: 10px;
            color: #333;
        `;

        // 创建垂直标尺
        const verticalRuler = document.createElement('div');
        verticalRuler.className = 'ruler vertical-ruler';
        verticalRuler.style.cssText = `
            position: absolute;
            top: 0;
            left: -20px;
            width: 20px;
            height: 100%;
            background: rgba(255,255,255,0.9);
            border-right: 1px solid #ccc;
            font-size: 10px;
            color: #333;
        `;

        // 添加刻度
        this.addRulerMarks(horizontalRuler, 'horizontal');
        this.addRulerMarks(verticalRuler, 'vertical');

        this.screen.appendChild(horizontalRuler);
        this.screen.appendChild(verticalRuler);
    }

    addRulerMarks(ruler, direction) {
        const isHorizontal = direction === 'horizontal';
        const maxSize = isHorizontal ? 854 : 480;
        const step = 50;

        for (let i = 0; i <= maxSize; i += step) {
            const mark = document.createElement('div');
            mark.textContent = i;
            mark.style.cssText = `
                position: absolute;
                ${isHorizontal ? `left: ${i}px; top: 5px;` : `top: ${i}px; left: 2px; writing-mode: vertical-rl;`}
                font-size: 8px;
                line-height: 1;
            `;
            ruler.appendChild(mark);
        }
    }

    removeRulers() {
        const rulers = this.screen.querySelectorAll('.ruler');
        rulers.forEach(ruler => ruler.remove());
    }

    resetZoom() {
        this.screen.style.transform = 'scale(0.7)';
    }

    initializeComponentDrag() {
        const componentItems = document.querySelectorAll('.component-item');
        
        componentItems.forEach(item => {
            item.addEventListener('click', () => {
                const componentType = item.dataset.component;
                this.addComponent(componentType);
            });
        });
    }

    addComponent(type) {
        const mainContent = this.screen.querySelector('.main-content');
        let component;

        switch(type) {
            case 'button':
                component = this.createButton();
                break;
            case 'menu':
                component = this.createMenu();
                break;
            case 'card':
                component = this.createCard();
                break;
            case 'list':
                component = this.createList();
                break;
            case 'form':
                component = this.createForm();
                break;
            case 'qr':
                component = this.createQRCode();
                break;
        }

        if (component && mainContent) {
            mainContent.appendChild(component);
        }
    }

    createButton() {
        const button = document.createElement('button');
        button.className = 'primary-btn';
        button.textContent = 'New Button';
        button.style.margin = '10px';
        return button;
    }

    createMenu() {
        const menu = document.createElement('div');
        menu.className = 'menu-items';
        menu.innerHTML = `
            <div class="menu-item">
                <span class="menu-number">1</span>
                <span class="menu-text">New Menu Item</span>
            </div>
        `;
        return menu;
    }

    createCard() {
        const card = document.createElement('div');
        card.style.cssText = `
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #3498db;
        `;
        card.innerHTML = `
            <h4>Card Title</h4>
            <p>Card content description</p>
        `;
        return card;
    }

    createList() {
        const list = document.createElement('ul');
        list.style.cssText = `
            list-style: none;
            padding: 0;
            margin: 10px 0;
        `;
        list.innerHTML = `
            <li style="padding: 8px; border-bottom: 1px solid rgba(255,255,255,0.2);">List Item 1</li>
            <li style="padding: 8px; border-bottom: 1px solid rgba(255,255,255,0.2);">List Item 2</li>
        `;
        return list;
    }

    createForm() {
        const form = document.createElement('div');
        form.style.cssText = `
            background: rgba(255,255,255,0.05);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        `;
        form.innerHTML = `
            <label style="display: block; margin-bottom: 5px; font-size: 12px;">Input Field:</label>
            <input type="text" style="width: 100%; padding: 8px; border: 1px solid #555; border-radius: 4px; background: rgba(255,255,255,0.1); color: white;">
        `;
        return form;
    }

    createQRCode() {
        const qrContainer = document.createElement('div');
        qrContainer.className = 'qr-section';
        qrContainer.innerHTML = `
            <div class="qr-code">
                <div class="qr-grid">
                    <div class="qr-corner top-left"></div>
                    <div class="qr-corner top-right"></div>
                    <div class="qr-corner bottom-left"></div>
                    <div class="qr-dots"></div>
                </div>
            </div>
            <div class="qr-instruction">
                <p>Touch to Scan QR.</p>
                <p>Scan QR code for user</p>
                <p>authentication.</p>
            </div>
        `;
        return qrContainer;
    }

    initializeMenuInteractions() {
        // 为菜单项添加交互效果
        document.addEventListener('click', (e) => {
            if (e.target.closest('.menu-item')) {
                const menuItem = e.target.closest('.menu-item');
                
                // 移除其他选中状态
                document.querySelectorAll('.menu-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                // 添加选中状态
                menuItem.classList.add('selected');
                
                // 添加选中样式
                menuItem.style.background = 'rgba(52, 152, 219, 0.5)';
                
                setTimeout(() => {
                    menuItem.style.background = 'rgba(255,255,255,0.1)';
                }, 200);
            }
        });
    }
}

// 初始化设计器
document.addEventListener('DOMContentLoaded', () => {
    new RealWearUIDesigner();
    
    // 添加一些实用功能
    addKeyboardShortcuts();
    addExportFunction();
});

function addKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey) {
            switch(e.key) {
                case 's':
                    e.preventDefault();
                    exportDesign();
                    break;
                case 'z':
                    e.preventDefault();
                    // Undo function (to be implemented)
                    console.log('Undo function');
                    break;
            }
        }
    });
}

function addExportFunction() {
    // Add export button
    const toolbar = document.querySelector('.toolbar .controls');
    const exportBtn = document.createElement('button');
    exportBtn.textContent = 'Export Design';
    exportBtn.addEventListener('click', exportDesign);
    toolbar.appendChild(exportBtn);
}

function exportDesign() {
    const screen = document.getElementById('realwearScreen');
    const designData = {
        html: screen.innerHTML,
        styles: getComputedStyle(screen).cssText,
        timestamp: new Date().toISOString(),
        resolution: '854x480'
    };
    
    const blob = new Blob([JSON.stringify(designData, null, 2)], {
        type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `realwear-ui-design-${Date.now()}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
    
    alert('Design exported successfully!');
}
