/* RealWear Smart Glass UI Designer Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f0f0;
    overflow: hidden;
}

.design-environment {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 工具栏样式 */
.toolbar {
    background: #2c3e50;
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.toolbar h1 {
    font-size: 18px;
    margin: 0;
}

.device-info {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #bdc3c7;
}

.controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.controls button, .controls select {
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    background: #34495e;
    color: white;
    cursor: pointer;
    font-size: 12px;
}

.controls button:hover {
    background: #4a6741;
}

/* 主设计区域 */
.design-area {
    display: flex;
    flex: 1;
    background: #ecf0f1;
}

/* RealWear设备模拟器 */
.device-simulator {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
}

.device-frame {
    background: #2c3e50;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    position: relative;
}

.device-frame::before {
    content: 'RealWear Navigator';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    color: #bdc3c7;
    font-size: 10px;
    font-weight: bold;
}

/* RealWear屏幕 - 精确的854x480分辨率 */
.screen {
    width: 854px;
    height: 480px;
    background: #000;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    transform: scale(0.65); /* 缩小以显示完整界面 */
    transform-origin: center;
}

/* UI容器 */
.ui-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    color: white;
    font-family: 'Arial', sans-serif;
    background: #000;
}

/* Header section */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #333;
}

.system-title {
    font-size: 18px;
    font-weight: bold;
    color: white;
    margin: 0;
}

.settings-icon {
    font-size: 20px;
    cursor: pointer;
}

/* Login section */
.login-section {
    flex: 1;
    display: flex;
    padding: 40px;
    gap: 60px;
    align-items: center;
}

.left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 60px;
}

.right-panel {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Company logo section */
.company-logo {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo-image {
    width: 190px;
    height: 190px;
    object-fit: contain;
}

.company-text {
    display: flex;
    flex-direction: column;
}

.company-name {
    font-size: 32px;
    font-weight: bold;
    color: white;
    letter-spacing: 1px;
}

.company-subtitle {
    font-size: 16px;
    color: #888;
    letter-spacing: 2px;
}

/* Login title section */
.login-title {
    text-align: left;
}

.login-title h2 {
    font-size: 48px;
    font-weight: bold;
    color: white;
    margin: 0 0 8px 0;
    line-height: 1;
}

.login-title p {
    font-size: 18px;
    color: #888;
    margin: 0;
}



/* Task Header */
.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #1e1e2e;
    border-bottom: 1px solid #313244;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.welcome-text {
    color: #cdd6f4;
    font-size: 16px;
    font-weight: 500;
}

.current-time {
    color: #a6adc8;
    font-size: 14px;
}

.step-info {
    display: flex;
    align-items: center;
}

.step-text {
    color: #a6adc8;
    font-size: 12px;
    font-weight: 500;
}

/* Capture Title */
.capture-title {
    color: #cdd6f4;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    text-align: left;
    padding-left: 5px;
    border-left: 4px solid #89b4fa;
    padding-left: 15px;
}

/* Capture Content */
.capture-content {
    display: flex;
    gap: 0;
    flex: 1;
    align-items: center;
    position: relative;
}

/* Content dividers */
.content-divider {
    width: 2px;
    height: 100%;
    background: #fca5a5; /* 浅红色分割线，在红色背景下可见 */
    margin: 0 30px;
    flex-shrink: 0;
}

.horizontal-divider {
    height: 2px;
    width: 100%;
    background: #fca5a5; /* 浅红色分割线，在红色背景下可见 */
    margin: 15px 0;
    flex-shrink: 0;
}

/* Image Display */
.image-display {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
}

.image-container {
    position: relative;
    width: 240px;
    height: 150px;
    border-radius: 12px;
    overflow: hidden;
    border: 3px solid #fca5a5; /* 浅红色边框，在红色背景下可见 */
}

.sample-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 25px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
    backdrop-filter: blur(1px);
}

.overlay-text-top {
    color: #ffffff;
    font-size: 28px;
    font-weight: 700;
    text-align: center;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
    letter-spacing: 1px;
}

.overlay-text-bottom {
    color: #ffffff;
    font-size: 42px;
    font-weight: 900;
    text-align: center;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
    letter-spacing: 2px;
}

/* Capture Info */
.capture-info {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.capture-label {
    color: #a6adc8;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
}

/* Capture Controls */
.capture-controls {
    display: flex;
    justify-content: center;
    gap: 30px;
    padding: 10px 0;
}

.control-btn {
    background: #7f1d1d; /* 深红色按钮背景 */
    border: 2px solid #991b1b; /* 深红色边框 */
    cursor: pointer;
    padding: 12px 15px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 55px;
    min-height: 55px;
}

.record-btn {
    color: #f38ba8;
}

.camera-btn {
    color: #89b4fa;
}

.btn-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
}

/* Capture Interface */
.capture-interface {
    display: flex;
    flex-direction: column;
    height: calc(100% - 60px);
    background: #dc2626; /* 红色警告背景 */
    padding: 15px;
    gap: 15px;
}

/* Main capture area with border */
.capture-main-area {
    flex: 1;
    border: 2px solid #fca5a5; /* 浅红色边框，在红色背景下可见 */
    border-radius: 12px;
    background: #991b1b; /* 深红色背景 */
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 0;
}

.capture-main-area:hover {
    border-color: #585b70;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: #181825;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    border-right: 1px solid #313244;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-placeholder {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #313244;
    border-radius: 8px;
    color: #a6adc8;
    font-size: 12px;
    font-weight: 500;
}

.sidebar-logo-text {
    display: flex;
    flex-direction: column;
}

.sidebar-company-name {
    font-size: 18px;
    font-weight: bold;
    color: #cdd6f4;
    letter-spacing: 1px;
}

.sidebar-company-subtitle {
    font-size: 10px;
    color: #a6adc8;
    letter-spacing: 2px;
}

/* Task Statistics */
.task-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-label {
    color: #a6adc8;
    font-size: 12px;
}

.stat-value {
    color: #cdd6f4;
    font-size: 24px;
    font-weight: bold;
}





/* Content Area */
.content-area {
    flex: 1;
    padding: 20px;
    background: #11111b;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Error Banner */
.error-banner {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 3px solid #dc3545;
    box-shadow: 0 3px 8px rgba(220, 53, 69, 0.4);
    margin-top: auto;
    margin-bottom: 5px;
}

.error-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.error-message {
    color: white;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
    flex: 1;
}

/* Task List */
.task-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

.task-item {
    background: #1e1e2e;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #89b4fa;
    transition: all 0.3s ease;
}

.task-item:hover {
    background: #262637;
    border-left-color: #74c7ec;
}

.task-name {
    color: #cdd6f4;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
}

.task-progress {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #313244;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #89b4fa, #74c7ec);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    color: #a6adc8;
    font-size: 14px;
    min-width: 30px;
}

.progress-percent {
    color: #a6adc8;
    font-size: 14px;
    min-width: 35px;
    text-align: right;
}

/* Machine Required Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-dialog {
    background: #313244;
    border-radius: 8px;
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    border: 1px solid #45475a;
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #45475a;
}

.modal-title {
    color: #cdd6f4;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.modal-body {
    padding: 20px 24px;
}

.modal-message {
    color: #cdd6f4;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 8px 0;
}

.modal-instruction {
    color: #a6adc8;
    font-size: 12px;
    margin: 0;
    font-style: italic;
}

.modal-footer {
    padding: 16px 24px 20px;
    display: flex;
    justify-content: flex-end;
}

.modal-btn-ok {
    background: #89b4fa;
    color: white;
    border: none;
    padding: 8px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.modal-btn-ok:hover {
    background: #74c7ec;
}





/* 组件面板 */
.component-panel {
    width: 200px;
    background: white;
    border-left: 1px solid #ddd;
    padding: 20px;
    overflow-y: auto;
}

.component-panel h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 14px;
}

.component-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.component-item {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.component-item:hover {
    background: #e9ecef;
    border-color: #3498db;
}

/* 属性面板 */
.properties-panel {
    width: 250px;
    background: white;
    border-left: 1px solid #ddd;
    padding: 20px;
    overflow-y: auto;
}

.properties-panel h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 14px;
}

.property-group {
    margin-bottom: 15px;
}

.property-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #555;
}

.property-group input {
    width: 100%;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.property-group input[type="range"] {
    margin-bottom: 5px;
}

/* 主题样式 */
.theme-dark .screen {
    background: #1a1a1a;
}

.theme-light .screen {
    background: #ffffff;
    color: #333;
}

.theme-light .status-bar {
    background: rgba(0,0,0,0.1);
    color: #333;
}

.theme-industrial .screen {
    background: #2c3e50;
}

.theme-industrial .menu-item {
    border-left-color: #f39c12;
}

.theme-industrial .menu-number {
    background: #f39c12;
}

/* 网格显示 */
.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background-image: 
        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .screen {
        transform: scale(0.6);
    }
}

@media (max-width: 1200px) {
    .screen {
        transform: scale(0.5);
    }
}

/* Modal Styles */
.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-container {
    background: #4a5568;
    border-radius: 8px;
    padding: 30px;
    min-width: 450px;
    max-width: 550px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 15px;
}

.modal-title {
    color: #f7fafc;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    text-align: left;
}

.modal-body {
    margin-bottom: 30px;
}

.modal-question {
    color: #e2e8f0;
    font-size: 16px;
    margin: 0 0 8px 0;
    text-align: left;
    line-height: 1.5;
}

.modal-hint {
    color: #a0aec0;
    font-size: 14px;
    margin: 0;
    text-align: left;
    font-style: italic;
}

.modal-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-btn {
    background: #4299e1;
    border: none;
    color: #ffffff;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 90px;
}

.modal-btn:hover {
    background: #3182ce;
}

.modal-btn:active {
    transform: scale(0.98);
}

.watch-btn {
    background: #4299e1;
}

.watch-btn:hover {
    background: #3182ce;
}

.proceed-btn {
    background: #4299e1;
    margin-left: 8px;
}

.proceed-btn:hover {
    background: #3182ce;
}
